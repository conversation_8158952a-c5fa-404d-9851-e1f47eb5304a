"use client";

import { useState } from "react";
import { WalletDialog, type TransactionData } from "@/components/wallet.dialog";

// Mock data for SODA transactions
const mockSodaTransactions: TransactionData = {
  "2025-01": [
    {
      id: "TXN001",
      date: "2025-01-31",
      type: "Yatırım",
      description: "Banka Havalesi ile Para Yatırma",
      amount: 2000,
      newBalance: 5150,
    },
    {
      id: "TXN002",
      date: "2025-01-30",
      type: "Ödeme",
      description: "Aktivite Ödemesi - PUBG Mobile Turnuvası",
      amount: -150,
      newBalance: 3150,
    },
    {
      id: "TXN003",
      date: "2025-01-29",
      type: "Kazanç",
      description: "Günlük Görev Tamamlama Ödülü",
      amount: 75,
      newBalance: 3300,
    },
    {
      id: "TXN004",
      date: "2025-01-28",
      type: "<PERSON>ek<PERSON>",
      description: "Para Çekme İşlemi <PERSON>",
      amount: -500,
      newBalance: 3225,
    },
    {
      id: "TXN005",
      date: "2025-01-27",
      type: "Hediye",
      description: "Arkadaştan SODA Hediyesi Alındı",
      amount: 200,
      newBalance: 3725,
    },
    {
      id: "TXN006",
      date: "2025-01-26",
      type: "Talep",
      description: "Çekim Talebi Oluşturuldu",
      amount: -300,
      newBalance: 3525,
      status: "pending",
    },
    {
      id: "TXN007",
      date: "2025-01-25",
      type: "Yatırım",
      description: "Kredi Kartı ile Para Yatırma",
      amount: 1500,
      newBalance: 3825,
    },
    {
      id: "TXN008",
      date: "2025-01-24",
      type: "Ödeme",
      description: "Aktivite Ödemesi - Valorant Turnuvası",
      amount: -400,
      newBalance: 2325,
    },
    {
      id: "TXN009",
      date: "2025-01-23",
      type: "Gönderim",
      description: "Arkadaşa SODA Hediyesi Gönderildi",
      amount: -100,
      newBalance: 2725,
    },
    {
      id: "TXN010",
      date: "2025-01-22",
      type: "Kazanç",
      description: "Haftalık Görev Tamamlama Bonusu",
      amount: 250,
      newBalance: 2825,
    },
    {
      id: "TXN011",
      date: "2025-01-21",
      type: "Yatırım",
      description: "Banka Havalesi ile Para Yatırma",
      amount: 800,
      newBalance: 2575,
    },
    {
      id: "TXN012",
      date: "2025-01-20",
      type: "Ödeme",
      description: "Aktivite Ödemesi - League of Legends Turnuvası",
      amount: -350,
      newBalance: 1775,
    },
    {
      id: "TXN013",
      date: "2025-01-19",
      type: "Hediye",
      description: "Arkadaştan SODA Hediyesi Alındı",
      amount: 150,
      newBalance: 2125,
    },
    {
      id: "TXN014",
      date: "2025-01-18",
      type: "Çekim",
      description: "Para Çekme İşlemi Tamamlandı",
      amount: -200,
      newBalance: 1975,
    },
    {
      id: "TXN015",
      date: "2025-01-17",
      type: "Kazanç",
      description: "Günlük Görev Tamamlama Ödülü",
      amount: 50,
      newBalance: 2175,
    },
    {
      id: "TXN016",
      date: "2025-01-16",
      type: "Talep",
      description: "Çekim Talebi Oluşturuldu",
      amount: -400,
      newBalance: 2125,
      status: "pending",
    },
    {
      id: "TXN017",
      date: "2025-01-15",
      type: "Yatırım",
      description: "Banka Havalesi ile Para Yatırma",
      amount: 1200,
      newBalance: 2525,
    },
    {
      id: "TXN018",
      date: "2025-01-14",
      type: "Ödeme",
      description: "Aktivite Ödemesi - CS2 Turnuvası",
      amount: -300,
      newBalance: 1325,
    },
    {
      id: "TXN019",
      date: "2025-01-13",
      type: "Gönderim",
      description: "Arkadaşa SODA Hediyesi Gönderildi",
      amount: -75,
      newBalance: 1625,
    },
    {
      id: "TXN020",
      date: "2025-01-12",
      type: "Kazanç",
      description: "Aktivite Tamamlama Ödülü",
      amount: 100,
      newBalance: 1700,
    },
  ],
  "2025-02": [
    {
      id: "TXN021",
      date: "2025-02-15",
      type: "Kazanç",
      description: "Aktivite Tamamlama Ödülü",
      amount: 150,
      newBalance: 5300,
    },
    {
      id: "TXN022",
      date: "2025-02-10",
      type: "Çekim",
      description: "Para Çekme İşlemi Tamamlandı",
      amount: -300,
      newBalance: 5150,
    },
  ],
  "2025-03": [
    {
      id: "TXN023",
      date: "2025-03-05",
      type: "Gönderim",
      description: "Arkadaşa SODA Hediyesi Gönderildi",
      amount: -50,
      newBalance: 5250,
    },
  ],
};

// Mock data for CAP transactions
const mockCapsTransactions: TransactionData = {
  "2025-01": [
    {
      id: "CAP001",
      date: "2025-01-15",
      type: "Dönüşüm",
      description: "SODA'dan CAP'e Dönüştürme (11 TL → 1 CAP)",
      amount: 1,
      newBalance: 25,
    },
    {
      id: "CAP002",
      date: "2025-01-10",
      type: "Kazanç",
      description: "Günlük Görev Tamamlama Ödülü",
      amount: 5,
      newBalance: 24,
    },
    {
      id: "CAP004",
      date: "2025-01-05",
      type: "Hediye",
      description: "Arkadaştan CAP Hediyesi Alındı",
      amount: 2,
      newBalance: 19,
    },
  ],
  "2025-02": [
    {
      id: "CAP003",
      date: "2025-02-08",
      type: "Gönderim",
      description: "Arkadaşa CAP Hediyesi Gönderildi",
      amount: -3,
      newBalance: 22,
    },
  ],
  "2025-03": [
    {
      id: "CAP005",
      date: "2025-03-12",
      type: "Kazanç",
      description: "Haftalık Görev Tamamlama Bonusu",
      amount: 10,
      newBalance: 32,
    },
  ],
};

export default function WalletDialogPage() {
  const [walletOpen, setWalletOpen] = useState(true);

  return (
    <WalletDialog
      open={walletOpen}
      onOpenChange={setWalletOpen}
      sodaBalance={5150}
      capsBalance={19}
      sodaTransactions={mockSodaTransactions}
      capsTransactions={mockCapsTransactions}
    />
  );
}
