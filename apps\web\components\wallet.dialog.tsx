"use client";

import * as React from "react";
import { useState } from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Receipt, ReceiptText, X, ChevronDown } from "lucide-react";
import { KYCDialog } from "./kyc.dialog";
import { BankDepositDialog } from "./bank-deposit.dialog";
import { IbanDialog } from "./iban.dialog";
import { WithdrawalDialog } from "./withdrawal.dialog";

// Transaction type definitions
export interface Transaction {
  id: string;
  date: string;
  type: string;
  description: string;
  amount: number;
  newBalance: number;
  status?: string;
}

export interface TransactionData {
  [month: string]: Transaction[];
}

export interface WalletDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  sodaBalance?: number;
  capsBalance?: number;
  sodaTransactions?: TransactionData;
  capsTransactions?: TransactionData;
  onDepositClick?: () => void;
  onBankTransferClick?: () => void;
  onWithdrawClick?: () => void;
  onEarnClick?: () => void;
  children?: React.ReactNode;
}

export function WalletDialog({
  open = false,
  onOpenChange,
  sodaTransactions = {},
  capsTransactions = {},
  onBankTransferClick,
  onWithdrawClick,
  onEarnClick,
  children,
}: WalletDialogProps) {
  const [activeTab, setActiveTab] = useState("soda");
  const [selectedMonth, setSelectedMonth] = useState("2025-01");

  // Child dialog states
  const [kycOpen, setKycOpen] = useState(false);
  const [bankDepositOpen, setBankDepositOpen] = useState(false);
  const [ibanOpen, setIbanOpen] = useState(false);
  const [withdrawalOpen, setWithdrawalOpen] = useState(false);

  // Child dialog handlers
  const handleBankTransferClick = () => {
    setKycOpen(true);
    if (onBankTransferClick) onBankTransferClick();
  };

  const handleWithdrawClick = () => {
    setIbanOpen(true);
    if (onWithdrawClick) onWithdrawClick();
  };

  const handleKycSubmit = () => {
    setKycOpen(false);
    // setBankDepositOpen(true);
  };

  const handleIbanSubmit = () => {
    setIbanOpen(false);
    setWithdrawalOpen(true);
  };

  // Generate months for current year
  const currentYear = new Date().getFullYear();
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    const monthStr = month.toString().padStart(2, "0");
    return {
      value: `${currentYear}-${monthStr}`,
      label: new Date(currentYear, i).toLocaleDateString("tr-TR", {
        month: "long",
        year: "numeric",
      }),
    };
  });

  const getCurrentTransactions = (): Transaction[] => {
    if (activeTab === "soda") {
      return sodaTransactions[selectedMonth] || [];
    } else {
      return capsTransactions[selectedMonth] || [];
    }
  };

  // Calculate balance for the selected month by finding the latest transaction's balance
  const getBalanceForMonth = (
    month: string,
    transactionData: TransactionData,
  ): number => {
    const transactions = transactionData[month] || [];
    if (transactions.length === 0) {
      // If no transactions for this month, find the latest transaction from previous months
      const allMonths = Object.keys(transactionData).sort();
      const currentMonthIndex = allMonths.indexOf(month);

      for (let i = currentMonthIndex - 1; i >= 0; i--) {
        const monthKey = allMonths[i];
        if (monthKey) {
          const prevMonthTransactions = transactionData[monthKey] || [];
          if (prevMonthTransactions.length > 0) {
            // Return the latest balance from the previous month
            const firstTransaction = prevMonthTransactions[0];
            return firstTransaction ? firstTransaction.newBalance : 0;
          }
        }
      }

      // If no previous transactions found, return 0
      return 0;
    }

    // Return the latest transaction's balance (first in array since they're sorted by date desc)
    const firstTransaction = transactions[0];
    return firstTransaction ? firstTransaction.newBalance : 0;
  };

  // Get current balance based on selected month and active tab
  const getCurrentBalance = (): number => {
    if (activeTab === "soda") {
      return getBalanceForMonth(selectedMonth, sodaTransactions);
    } else {
      return getBalanceForMonth(selectedMonth, capsTransactions);
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    const sign = amount >= 0 ? "+" : "";
    return `${sign}${amount} ${currency}`;
  };

  // Hide parent dialog when child dialogs are open
  const isParentDialogVisible = !kycOpen && !ibanOpen;

  return (
    <Dialog open={open && isParentDialogVisible} onOpenChange={onOpenChange}>
      <DialogContent
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className="sm:max-w-[1000px] h-[600px] overflow-hidden w-full max-w-full flex flex-col pb-10"
      >
        <div className="w-full flex flex-col flex-1">
          <DialogHeader className="flex-row items-center justify-between gap-4">
            <DialogTitle variant="stripe" className="w-2/3">
              {"CÜZDAN"}
            </DialogTitle>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mr-10 h-15 text-2xl font-mono font-bold">
                <TabsTrigger value="soda" className="w-50 relative first:pl-4">
                  <div className="flex items-center justify-between gap-3">
                    <div className="w-14 h-14 drop-shadow-md/40">
                      <Image
                        src="/assets/soda-lg.png"
                        alt="Soda"
                        fill
                        sizes="150px"
                        className="object-contain"
                      />
                    </div>
                    <div className="text-right">
                      {getBalanceForMonth(selectedMonth, sodaTransactions)}
                    </div>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="caps" className="w-50 relative pl-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="w-12 h-12 drop-shadow-md/40">
                      <Image
                        src="/assets/cap-lg.png"
                        alt="Cap"
                        fill
                        sizes="100px"
                        className="object-contain"
                      />
                    </div>
                    <div>
                      {getBalanceForMonth(selectedMonth, capsTransactions)}
                    </div>
                  </div>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </DialogHeader>

          <div className="flex flex-col flex-1 space-y-6 mx-6 mt-6">
            {/* Month selector and action buttons */}
            <div className="flex justify-between items-center">
              <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Ay seçin" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex gap-3">
                {activeTab === "soda" ? (
                  <>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          size="small"
                          variant="default"
                          className="px-10 flex items-center gap-2"
                        >
                          {"YATIR"}
                          <ChevronDown className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <DropdownMenuItem onClick={handleBankTransferClick}>
                          Banka Transferi
                        </DropdownMenuItem>
                        <DropdownMenuItem disabled>
                          Kredi Kartı
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <Button
                      onClick={handleWithdrawClick}
                      size="small"
                      variant="default"
                      className="px-10"
                    >
                      {"ÇEK"}
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={onEarnClick}
                    disabled
                    size="small"
                    variant="default"
                    className="px-10"
                  >
                    {"KAZAN"}
                  </Button>
                )}
              </div>
            </div>

            {/* Transactions table */}
            <div className="border-2 rounded-lg overflow-hidden w-full flex-1 flex flex-col min-h-0 max-h-[400px]">
              <div className="flex-1 overflow-y-auto min-h-0 scrollbar max-h-[350px]">
                <Table className="w-full">
                  <TableHeader className="sticky top-0 bg-background z-10">
                    <TableRow>
                      <TableHead className="w-24 min-w-[6rem] flex-shrink-0">
                        Tarih
                      </TableHead>
                      <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                        No
                      </TableHead>
                      <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                        Tür
                      </TableHead>
                      <TableHead className="flex-1 min-w-0">Açıklama</TableHead>
                      <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                        Miktar
                      </TableHead>
                      <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                        Bakiye
                      </TableHead>
                      <TableHead className="w-12 min-w-[3rem] flex-shrink-0"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getCurrentTransactions().length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={7}
                          className="text-center text-muted-foreground py-8"
                        >
                          Bu ay için işlem bulunamadı
                        </TableCell>
                      </TableRow>
                    ) : (
                      getCurrentTransactions().map((transaction) => (
                        <TableRow
                          key={transaction.id}
                          className={
                            transaction.type === "Talep" ? "bg-primary/30" : ""
                          }
                        >
                          <TableCell className="w-24 min-w-[6rem] flex-shrink-0">
                            {new Date(transaction.date).toLocaleDateString(
                              "tr-TR",
                            )}
                          </TableCell>
                          <TableCell className="w-20 min-w-[5rem] flex-shrink-0 font-mono text-sm">
                            {transaction.id}
                          </TableCell>
                          <TableCell className="w-20 min-w-[5rem] flex-shrink-0">
                            {transaction.type}
                          </TableCell>
                          <TableCell className="flex-1 min-w-0 max-w-30">
                            <div
                              className="truncate"
                              title={transaction.description}
                            >
                              {transaction.description}
                            </div>
                          </TableCell>
                          <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                            <span
                              className={
                                transaction.amount >= 0
                                  ? "text-green-600"
                                  : "text-red-600"
                              }
                            >
                              {transaction.amount}
                            </span>
                          </TableCell>
                          <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right font-medium">
                            {transaction.newBalance}
                          </TableCell>
                          <TableCell className="w-12 min-w-[3rem] flex-shrink-0">
                            {transaction.type === "Talep" &&
                            (transaction as any).status === "pending" ? (
                              <button className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-red-600 hover:text-red-700">
                                <X className="w-5 h-5" />
                              </button>
                            ) : transaction.type === "Yatırım" ||
                              transaction.type === "Çekim" ? (
                              <button className="border-none shadow-none bg-transparent p-1 hover:bg-transparent focus:outline-none text-foreground hover:text-foreground/80">
                                <ReceiptText className="w-5 h-5" />
                              </button>
                            ) : null}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Child Dialogs */}
      <KYCDialog
        open={kycOpen}
        onOpenChange={setKycOpen}
        onSubmit={handleKycSubmit}
      />

      {/* <IbanDialog
        open={ibanOpen}
        onOpenChange={setIbanOpen}
        onSubmit={handleIbanSubmit}
      >
        <WithdrawalDialog
          open={withdrawalOpen}
          onOpenChange={setWithdrawalOpen}
          balance={getBalanceForMonth(selectedMonth, sodaTransactions)}
          min={20}
          max={5000}
          onSubmit={(data) => {
            console.log("Withdrawal submitted:", data);
            setWithdrawalOpen(false);
          }}
        />
      </IbanDialog> */}

      {children}
    </Dialog>
  );
}
